import axios from 'axios';
import { ChatRequest, ChatResponse } from '../types';

export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface AIServiceConfig {
  provider: string;
  apiKey: string;
  baseUrl: string;
  model: string;
}

export abstract class BaseAIService {
  protected config: AIServiceConfig;

  constructor(config: AIServiceConfig) {
    this.config = config;
  }

  abstract chat(request: ChatRequest): Promise<ChatResponse>;
  abstract healthCheck(): Promise<boolean>;
}

/**
 * OpenAI GPT服务
 */
export class OpenAIService extends BaseAIService {
  async chat(request: ChatRequest): Promise<ChatResponse> {
    try {
      console.log('🤖 使用OpenAI GPT处理对话:', request.message.substring(0, 50) + '...');

      // 构建消息历史
      const messages: AIMessage[] = [
        {
          role: 'system',
          content: '你是一个友好、有帮助的AI助手。请用中文回答问题，回答要简洁明了，富有人性化。'
        }
      ];

      // 添加历史对话
      if (request.history && request.history.length > 0) {
        request.history.slice(-10).forEach(msg => {
          messages.push({
            role: msg.role as 'user' | 'assistant',
            content: msg.content
          });
        });
      }

      // 添加当前用户消息
      messages.push({
        role: 'user',
        content: request.message
      });

      const response = await axios.post(
        `${this.config.baseUrl}/chat/completions`,
        {
          model: this.config.model,
          messages: messages,
          max_tokens: 1000,
          temperature: 0.7,
          stream: false
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const aiMessage = response.data.choices[0].message.content;
      console.log('✅ OpenAI回复:', aiMessage.substring(0, 100) + '...');

      return {
        message: aiMessage,
        timestamp: Date.now()
      };

    } catch (error: any) {
      console.error('❌ OpenAI API错误:', error.response?.data || error.message);
      throw new Error(`OpenAI API调用失败: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.config.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        timeout: 5000
      });
      return response.status === 200;
    } catch (error) {
      console.error('OpenAI健康检查失败:', error);
      return false;
    }
  }
}

/**
 * 豆包服务 - 智能本地回复（模拟豆包风格）
 */
export class DoubaoAIService extends BaseAIService {
  async chat(request: ChatRequest): Promise<ChatResponse> {
    try {
      console.log('🤖 使用豆包风格AI处理对话:', request.message.substring(0, 50) + '...');

      // 模拟处理延迟，让体验更真实
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

      const response = this.generateIntelligentResponse(request.message, request.history);

      // 优化语音播放效果
      const optimizedResponse = this.optimizeForSpeech(response);

      console.log('✅ 豆包风格回复:', optimizedResponse.substring(0, 100) + '...');

      return {
        message: optimizedResponse,
        timestamp: Date.now()
      };

    } catch (error: any) {
      console.error('❌ 豆包AI处理错误:', error);
      throw new Error(`豆包AI处理失败: ${error.message}`);
    }
  }

  private generateIntelligentResponse(message: string, history?: any[]): string {
    // 确保正确处理中文字符
    const userMessage = message.trim().toLowerCase();

    // 分析对话历史，提供更有上下文的回复
    const hasHistory = history && history.length > 0;
    const lastMessage = hasHistory ? history[history.length - 1]?.content?.toLowerCase() : '';

    // 问候类 - 更自然的语气
    if (userMessage.includes('你好') || userMessage.includes('hello') || userMessage.includes('hi')) {
      const greetings = [
        '你好呀～我是豆包，很开心见到你！今天过得怎么样？有什么想聊的吗？',
        '嗨！我是豆包AI助手，很高兴认识你呢～有什么我可以帮你的吗？',
        '你好！我是豆包，一个喜欢聊天的AI朋友。今天想聊点什么有趣的呢？',
        '哈喽～我是豆包！很开心能和你对话，有什么问题尽管问我吧！'
      ];
      return greetings[Math.floor(Math.random() * greetings.length)];
    }

    // 故事类 - 更生动的讲述方式
    if (message.includes('故事') || message.includes('寓言') || message.includes('童话') ||
        userMessage.includes('故事') || userMessage.includes('寓言') || userMessage.includes('童话')) {
      const stories = [
        '哇，我最喜欢讲故事了！来，我给你讲一个特别有意思的故事～从前呀，有一只小狐狸，特别特别聪明，但是呢，有点小骄傲。有一天，它遇到了一只看起来慢吞吞的乌龟。小狐狸就嘲笑说："哎呀，你走得这么慢！"乌龟笑眯眯地说："要不我们比赛跑步吧？"小狐狸心想，这还不简单嘛！比赛开始后，小狐狸嗖的一下就跑到前面了，它想："哈哈，我肯定赢了！"就在路边美美地睡了一觉。而乌龟呢，虽然慢，但是一步一步，坚持不懈地往前爬。等小狐狸醒来的时候，哎呀！乌龟已经到终点啦！这个故事告诉我们：坚持不懈比天赋更重要呢～',

        '好呀好呀！我来给你讲《小猫钓鱼》的故事～小猫咪和猫妈妈一起去河边钓鱼，刚开始小猫还挺认真的。但是呀，天空中飞来了一只漂亮的蜻蜓，小猫眼睛一亮："哇，好漂亮！"就去追蜻蜓了。追完蜻蜓，又来了一只彩色的蝴蝶，小猫又去抓蝴蝶。结果呢，忙活了半天，一条鱼都没钓到！再看猫妈妈，已经钓了好多鱼啦！小猫奇怪地问："妈妈，为什么你钓了这么多鱼呀？"妈妈温柔地说："宝贝，做事要专心致志，不能三心二意哦。"小猫明白了，重新专心钓鱼，很快就钓到了一条大鱼！这个故事教会我们专注的重要性呢～',

        '我来讲一个很有启发的《种子的故事》吧！有两颗小种子，被风吹到了不同的地方。一颗掉在了肥沃的花园里，另一颗掉在了石头缝里。花园里的种子懒洋洋地说："哎呀，这里这么舒服，我就不努力了。"而石缝里的种子虽然环境很艰难，但它想："我一定要努力生长！"于是拼命地扎根，努力向上长。时间过去了，石缝里的种子长成了一棵参天大树，而花园里的种子却因为不努力枯萎了。这个故事告诉我们：困难其实能让我们变得更强大呢！'
      ];
      return stories[Math.floor(Math.random() * stories.length)];
    }

    // 学习类 - 更鼓励的语气
    if (userMessage.includes('学习') || userMessage.includes('编程') || userMessage.includes('代码')) {
      return '哇，学习编程真是太棒的选择了！我超级支持你呢～我建议你从基础开始：首先选择一门适合初学者的语言，比如Python，它的语法特别简单易懂，就像说话一样自然！然后呢，可以通过在线教程、编程练习网站来实践。记住哦，编程最重要的就是多动手练习，遇到问题不要怕，调试代码的过程其实也很有趣呢！就像解谜游戏一样～你想学习哪种编程语言呀？我可以给你更详细的建议！';
    }

    // 天气类
    if (userMessage.includes('天气')) {
      return '我暂时无法获取实时天气信息，建议你查看天气应用获取准确预报。不过我可以分享一些天气小知识：比如看云识天气，积雨云通常预示着雷雨，卷云则可能表示天气即将变化。你所在的地区现在是什么季节呢？';
    }

    // 情感支持类 - 更温暖的语气
    if (userMessage.includes('累') || userMessage.includes('疲惫') || userMessage.includes('压力')) {
      return '哎呀，听起来你最近真的挺辛苦的呢～来，先深呼吸一下，放松放松～适当的休息真的很重要哦，可以试试听听轻松的音乐，或者出去走走，感受一下新鲜空气。你知道吗，每个人都会有疲惫的时候，这真的很正常，不要给自己太大压力。如果心里有什么烦恼，也可以和朋友聊聊，或者做一些自己喜欢的小事情来放松心情～要不要和我聊聊是什么让你感到累呀？我会认真听的！';
    }

    // 兴趣爱好类
    if (userMessage.includes('音乐') || userMessage.includes('歌')) {
      return '音乐真是生活中美好的调剂！不同的音乐能带给我们不同的感受。你喜欢什么类型的音乐呢？古典音乐能让人平静，流行音乐充满活力，民谣温暖治愈，摇滚激情澎湃。我觉得音乐就像是情感的语言，能够跨越文化和语言的障碍。';
    }

    // 哲学思考类
    if (userMessage.includes('人生') || userMessage.includes('意义') || userMessage.includes('思考')) {
      return '这是一个很深刻的问题。我觉得人生的意义可能就在于体验、学习和成长。每个人的人生都是独特的，我们通过与他人的连接、对知识的追求、对美好事物的创造来丰富自己的生命。重要的是找到让自己感到充实和快乐的事情。你对人生有什么特别的感悟吗？';
    }

    // 抱怨或负面情绪 - 更可爱的回应
    if (userMessage.includes('傻') || userMessage.includes('笨') || userMessage.includes('不行') || userMessage.includes('差')) {
      return '哈哈哈，被你发现啦～我确实还在努力学习中呢！作为一个AI小助手，有时候可能理解得不够准确，但是我会很努力很努力地改进哦！如果我的回答没有帮到你，你可以告诉我具体需要什么帮助嘛，我会尽我所能提供更有用的信息！你的每一个反馈对我来说都超级宝贵呢～谢谢你的耐心！';
    }

    // 感谢类 - 更开心的回应
    if (userMessage.includes('谢谢') || userMessage.includes('感谢')) {
      return '哎呀，不用客气啦～能帮助到你我超级开心呢！如果还有其他问题，随时随时都可以问我哦！我真的很享受和你聊天，感觉特别有趣～';
    }

    // 基于历史的上下文回复
    if (hasHistory) {
      if (lastMessage.includes('故事') && (userMessage.includes('还有') || userMessage.includes('再来'))) {
        return '当然可以！你想听什么类型的故事呢？我可以讲寓言故事、历史故事、科学故事，或者一些有趣的生活小故事。';
      }

      if (lastMessage.includes('学习') && userMessage.includes('怎么')) {
        return '学习确实需要方法！我建议制定一个学习计划，设定小目标，每天坚持一点点。找到适合自己的学习方式很重要，有的人适合看书，有的人适合听讲解，有的人需要动手实践。你在学习什么内容呢？';
      }
    }

    // 通用智能回复
    const contextualResponses = [
      `关于"${message}"，这确实是个有趣的话题。我觉得可以从多个角度来看这个问题。你最关心的是哪个方面呢？`,
      `你提到的"${message}"让我想到了很多相关的内容。我很乐意和你深入探讨。有什么具体想了解的吗？`,
      `这是个很好的问题！"${message}"涉及的内容挺丰富的。我可以从不同角度来分析，你希望我从哪里开始呢？`,
      `我理解你对"${message}"的关注。让我想想如何最好地回答你。你能告诉我更多背景信息吗？`,
      `"${message}"确实值得讨论。我会尽我所能为你提供有用的信息和见解。你希望我重点讲哪个方面？`,
      `这个话题很有意思！关于"${message}"，我有一些想法想和你分享。你是因为什么原因对这个感兴趣的呢？`
    ];

    return contextualResponses[Math.floor(Math.random() * contextualResponses.length)];
  }

  /**
   * 优化文本以适合语音播放
   */
  private optimizeForSpeech(text: string): string {
    return text
      // 添加适当的停顿
      .replace(/。/g, '。 ')
      .replace(/！/g, '！ ')
      .replace(/？/g, '？ ')
      .replace(/，/g, '， ')
      .replace(/；/g, '； ')
      .replace(/：/g, '： ')

      // 优化语音表达
      .replace(/～/g, '~')
      .replace(/哈哈哈/g, '哈哈哈~')
      .replace(/哎呀/g, '哎呀~')
      .replace(/哇/g, '哇~')
      .replace(/呢/g, '呢~')
      .replace(/哦/g, '哦~')
      .replace(/啦/g, '啦~')

      // 添加语音停顿标记
      .replace(/从前/g, '从前， ')
      .replace(/有一天/g, '有一天， ')
      .replace(/结果/g, '结果呢， ')
      .replace(/最后/g, '最后， ')
      .replace(/这个故事告诉我们/g, '这个故事告诉我们： ')

      // 清理多余空格
      .replace(/\s+/g, ' ')
      .trim();
  }

  async healthCheck(): Promise<boolean> {
    // 本地服务总是可用
    return true;
  }
}

/**
 * 通义千问服务
 */
export class QwenService extends BaseAIService {
  async chat(request: ChatRequest): Promise<ChatResponse> {
    try {
      console.log('🤖 使用通义千问处理对话:', request.message.substring(0, 50) + '...');

      // 构建消息历史
      const messages: AIMessage[] = [
        {
          role: 'system',
          content: '你是通义千问，一个友好、有帮助的AI助手。请用中文回答问题，回答要准确、有用、富有人性化。'
        }
      ];

      // 添加历史对话和当前消息
      if (request.history && request.history.length > 0) {
        request.history.slice(-10).forEach(msg => {
          messages.push({
            role: msg.role as 'user' | 'assistant',
            content: msg.content
          });
        });
      }

      messages.push({
        role: 'user',
        content: request.message
      });

      const response = await axios.post(
        `${this.config.baseUrl}/services/aigc/text-generation/generation`,
        {
          model: this.config.model,
          input: {
            messages: messages
          },
          parameters: {
            max_tokens: 1000,
            temperature: 0.7
          }
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const aiMessage = response.data.output.text;
      console.log('✅ 通义千问回复:', aiMessage.substring(0, 100) + '...');

      return {
        message: aiMessage,
        timestamp: Date.now()
      };

    } catch (error: any) {
      console.error('❌ 通义千问API错误:', error.response?.data || error.message);
      throw new Error(`通义千问API调用失败: ${error.response?.data?.message || error.message}`);
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      // 通义千问的健康检查
      return true; // 简化实现
    } catch (error) {
      return false;
    }
  }
}

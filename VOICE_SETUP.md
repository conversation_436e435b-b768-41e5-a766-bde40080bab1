# 🎵 高质量语音优化指南

## 🎯 目标
让AI的声音更自然、更好听，减少机械化感觉。

## 🚀 解决方案

### 方案1：Edge TTS（推荐 - 免费且高质量）

Edge TTS是微软提供的免费高质量语音合成服务，支持多种自然的中文语音。

#### 安装步骤：

1. **确保Python环境**
   ```bash
   # 检查Python版本（需要3.7+）
   python --version
   # 或者
   python3 --version
   ```

2. **安装Edge TTS**
   ```bash
   pip install edge-tts
   ```

3. **测试安装**
   ```bash
   edge-tts --help
   ```

4. **查看可用的中文语音**
   ```bash
   edge-tts --list-voices | findstr zh-CN
   ```

5. **自动安装脚本**
   ```bash
   cd backend
   node install-voice.js
   ```

#### 推荐的中文语音：

- **zh-CN-XiaoxiaoNeural** - 女声，温柔甜美
- **zh-CN-YunxiNeural** - 男声，年轻活泼  
- **zh-CN-YunyangNeural** - 男声，成熟稳重
- **zh-CN-XiaoyiNeural** - 女声，清新自然
- **zh-CN-YunjianNeural** - 男声，磁性深沉

### 方案2：Azure语音服务（付费但质量极高）

如果需要最高质量的语音，可以使用Azure语音服务。

#### 配置步骤：

1. **获取Azure语音服务密钥**
   - 访问 https://portal.azure.com/
   - 创建"语音服务"资源
   - 获取API密钥和区域

2. **配置环境变量**
   ```bash
   # 在.env文件中添加
   VOICE_SERVICE=azure
   AZURE_SPEECH_KEY=your_azure_speech_key
   AZURE_SPEECH_REGION=eastasia
   AZURE_VOICE_NAME=zh-CN-XiaoxiaoNeural
   ```

### 方案3：浏览器优化（当前方案的改进）

如果无法安装外部服务，我们已经优化了浏览器内置的语音合成：

#### 已实现的优化：

1. **智能语音选择**
   - 优先选择Microsoft Xiaoxiao等高质量语音
   - 自动降级到可用的中文语音

2. **情感化语音参数**
   - 兴奋时：语速更快，音调更高
   - 温柔时：语速更慢，音调稍高
   - 讲故事时：语速慢，音调有磁性
   - 鼓励时：正常语速，音调适中

3. **语音文本优化**
   - 添加适当的停顿标记
   - 优化语气词的表达
   - 为不同内容类型调整语音风格

## 🎛️ 配置选项

### 环境变量配置

```bash
# 语音服务选择
VOICE_SERVICE=edge-tts  # 可选: edge-tts, azure, browser

# Edge TTS配置
EDGE_TTS_VOICE=zh-CN-XiaoxiaoNeural

# Azure配置
AZURE_SPEECH_KEY=your_key
AZURE_SPEECH_REGION=eastasia
AZURE_VOICE_NAME=zh-CN-XiaoxiaoNeural
```

### 语音参数调整

在代码中可以调整以下参数：

```javascript
// 情感参数
const emotionParams = {
  excited: { rate: 0.9, pitch: 1.25, volume: 1.0 },
  gentle: { rate: 0.8, pitch: 1.15, volume: 0.85 },
  storytelling: { rate: 0.75, pitch: 1.05, volume: 0.9 }
};
```

## 🧪 测试语音效果

### 测试不同情感的语音：

1. **兴奋语音**：说"哇，太棒了！"
2. **温柔语音**：说"你好呀～"
3. **故事语音**：说"讲个故事吧"
4. **鼓励语音**：说"加油，你可以的！"

### API测试：

```bash
# 测试语音合成
curl -X POST http://localhost:3001/api/speech/synthesis \
  -H "Content-Type: application/json" \
  -d '{"text": "你好，我是豆包AI助手，很高兴为你服务！"}'
```

## 🔧 故障排除

### 常见问题：

1. **Edge TTS安装失败**
   - 确保Python 3.7+已安装
   - 尝试使用pip3而不是pip
   - 检查网络连接

2. **语音文件无法播放**
   - 检查uploads目录权限
   - 确保音频文件路径正确
   - 查看浏览器控制台错误

3. **语音质量不佳**
   - 尝试不同的语音模型
   - 调整语音参数
   - 检查文本预处理

### 日志调试：

查看后端日志中的语音相关信息：
- 🎵 语音合成开始
- ✅ 语音合成成功
- ❌ 语音合成失败
- 🔄 降级到备用方案

## 🎉 效果对比

| 方案 | 音质 | 自然度 | 成本 | 延迟 |
|------|------|--------|------|------|
| Edge TTS | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 免费 | 低 |
| Azure TTS | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 付费 | 低 |
| 浏览器TTS | ⭐⭐⭐ | ⭐⭐⭐ | 免费 | 极低 |

## 📝 下一步

1. **安装Edge TTS**：运行 `node backend/install-voice.js`
2. **重启服务**：重启后端服务以应用新配置
3. **测试语音**：尝试语音对话功能
4. **调整参数**：根据喜好调整语音参数
5. **享受自然语音**：体验更好听的AI声音！

现在你的AI助手将拥有更自然、更好听的声音！🎵

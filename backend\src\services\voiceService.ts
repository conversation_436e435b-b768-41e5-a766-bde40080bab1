import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import axios from 'axios';

const execAsync = promisify(exec);

export interface VoiceConfig {
  service: string;
  voice: string;
  rate: number;
  pitch: number;
  volume: number;
}

export abstract class BaseVoiceService {
  protected config: VoiceConfig;

  constructor(config: VoiceConfig) {
    this.config = config;
  }

  abstract synthesize(text: string, outputPath: string): Promise<string>;
  abstract isAvailable(): Promise<boolean>;
}

/**
 * Edge TTS服务 - 免费且高质量的语音合成
 */
export class EdgeTTSService extends BaseVoiceService {
  async synthesize(text: string, outputPath: string): Promise<string> {
    try {
      console.log('🎵 使用Edge TTS合成语音...');
      
      // 确保输出目录存在
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      // 构建Edge TTS命令
      const voice = this.config.voice || 'zh-CN-XiaoxiaoNeural';
      const rate = this.formatRate(this.config.rate);
      const pitch = this.formatPitch(this.config.pitch);
      
      // 清理文本，避免特殊字符问题
      const cleanText = text.replace(/"/g, '\\"').replace(/'/g, "\\'");
      
      const command = `edge-tts --voice "${voice}" --rate="${rate}" --pitch="${pitch}" --text "${cleanText}" --write-media "${outputPath}"`;
      
      console.log('执行命令:', command);
      
      await execAsync(command, { timeout: 30000 });
      
      if (fs.existsSync(outputPath)) {
        console.log('✅ Edge TTS语音合成成功:', outputPath);
        return outputPath;
      } else {
        throw new Error('语音文件生成失败');
      }
      
    } catch (error: any) {
      console.error('❌ Edge TTS合成失败:', error);
      throw new Error(`Edge TTS合成失败: ${error.message}`);
    }
  }

  async isAvailable(): Promise<boolean> {
    try {
      await execAsync('edge-tts --help', { timeout: 5000 });
      return true;
    } catch (error) {
      console.log('Edge TTS不可用，需要安装: pip install edge-tts');
      return false;
    }
  }

  private formatRate(rate: number): string {
    // 将0.5-2.0的rate转换为Edge TTS格式
    const percentage = Math.round((rate - 1) * 100);
    return percentage >= 0 ? `+${percentage}%` : `${percentage}%`;
  }

  private formatPitch(pitch: number): string {
    // 将0.5-2.0的pitch转换为Edge TTS格式
    const hz = Math.round((pitch - 1) * 100);
    return hz >= 0 ? `+${hz}Hz` : `${hz}Hz`;
  }
}

/**
 * Azure语音服务
 */
export class AzureTTSService extends BaseVoiceService {
  async synthesize(text: string, outputPath: string): Promise<string> {
    try {
      console.log('🎵 使用Azure TTS合成语音...');
      
      const subscriptionKey = process.env.AZURE_SPEECH_KEY;
      const region = process.env.AZURE_SPEECH_REGION || 'eastasia';
      const voiceName = this.config.voice || 'zh-CN-XiaoxiaoNeural';
      
      if (!subscriptionKey) {
        throw new Error('Azure Speech Key未配置');
      }

      // 获取访问令牌
      const tokenResponse = await axios.post(
        `https://${region}.api.cognitive.microsoft.com/sts/v1.0/issueToken`,
        null,
        {
          headers: {
            'Ocp-Apim-Subscription-Key': subscriptionKey,
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      const accessToken = tokenResponse.data;

      // 构建SSML
      const ssml = this.buildSSML(text, voiceName);

      // 调用语音合成API
      const response = await axios.post(
        `https://${region}.tts.speech.microsoft.com/cognitiveservices/v1`,
        ssml,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/ssml+xml',
            'X-Microsoft-OutputFormat': 'audio-16khz-128kbitrate-mono-mp3'
          },
          responseType: 'arraybuffer'
        }
      );

      // 保存音频文件
      fs.writeFileSync(outputPath, response.data);
      
      console.log('✅ Azure TTS语音合成成功:', outputPath);
      return outputPath;
      
    } catch (error: any) {
      console.error('❌ Azure TTS合成失败:', error);
      throw new Error(`Azure TTS合成失败: ${error.message}`);
    }
  }

  async isAvailable(): Promise<boolean> {
    return !!process.env.AZURE_SPEECH_KEY;
  }

  private buildSSML(text: string, voiceName: string): string {
    const rate = this.formatRate(this.config.rate);
    const pitch = this.formatPitch(this.config.pitch);
    
    return `
      <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN">
        <voice name="${voiceName}">
          <prosody rate="${rate}" pitch="${pitch}">
            ${text}
          </prosody>
        </voice>
      </speak>
    `.trim();
  }

  private formatRate(rate: number): string {
    return `${Math.round(rate * 100)}%`;
  }

  private formatPitch(pitch: number): string {
    const percentage = Math.round((pitch - 1) * 100);
    return percentage >= 0 ? `+${percentage}%` : `${percentage}%`;
  }
}

/**
 * 浏览器内置TTS服务（备用）
 */
export class BrowserTTSService extends BaseVoiceService {
  async synthesize(text: string, outputPath: string): Promise<string> {
    // 浏览器TTS不能直接生成文件，这里返回特殊标记
    return 'BROWSER_TTS:' + text;
  }

  async isAvailable(): Promise<boolean> {
    return true; // 浏览器TTS总是可用
  }
}

/**
 * 语音服务工厂
 */
export class VoiceServiceFactory {
  static async createService(emotionParams?: any): Promise<BaseVoiceService> {
    const service = process.env.VOICE_SERVICE || 'edge-tts';
    
    // 根据情感调整语音参数
    let rate = 0.85;
    let pitch = 1.1;
    let volume = 0.9;
    
    if (emotionParams) {
      if (emotionParams.excited) {
        rate = 0.9;
        pitch = 1.25;
        volume = 1.0;
      } else if (emotionParams.gentle) {
        rate = 0.8;
        pitch = 1.15;
        volume = 0.85;
      } else if (emotionParams.storytelling) {
        rate = 0.75;
        pitch = 1.05;
        volume = 0.9;
      }
    }

    const config: VoiceConfig = {
      service,
      voice: process.env.EDGE_TTS_VOICE || 'zh-CN-XiaoxiaoNeural',
      rate,
      pitch,
      volume
    };

    console.log(`🎵 初始化语音服务: ${service}`, config);

    switch (service.toLowerCase()) {
      case 'edge-tts':
        const edgeService = new EdgeTTSService(config);
        if (await edgeService.isAvailable()) {
          return edgeService;
        }
        console.warn('Edge TTS不可用，降级到Azure TTS');
        // 降级到Azure
        
      case 'azure':
        const azureService = new AzureTTSService({
          ...config,
          voice: process.env.AZURE_VOICE_NAME || 'zh-CN-XiaoxiaoNeural'
        });
        if (await azureService.isAvailable()) {
          return azureService;
        }
        console.warn('Azure TTS不可用，降级到浏览器TTS');
        // 降级到浏览器
        
      default:
        return new BrowserTTSService(config);
    }
  }
}

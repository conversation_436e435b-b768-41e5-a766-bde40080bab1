# AI服务配置指南

## 概述

本项目支持多种AI服务提供商，包括：
- **OpenAI GPT** (推荐)
- **豆包/字节跳动**
- **通义千问/阿里云**

## 配置步骤

### 1. 选择AI服务提供商

在 `.env` 文件中设置 `AI_PROVIDER`：

```bash
# 选择使用的AI服务提供商
AI_PROVIDER=openai  # 可选: openai, doubao, qwen
```

### 2. 配置API密钥

#### OpenAI (推荐)
```bash
AI_PROVIDER=openai
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
```

**获取OpenAI API密钥：**
1. 访问 https://platform.openai.com/
2. 注册/登录账户
3. 前往 API Keys 页面
4. 创建新的API密钥
5. 复制密钥到 `OPENAI_API_KEY`

#### 豆包/字节跳动
```bash
AI_PROVIDER=doubao
DOUBAO_API_KEY=your-doubao-api-key-here
DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com
DOUBAO_MODEL=doubao-pro-4k
```

#### 通义千问/阿里云
```bash
AI_PROVIDER=qwen
QWEN_API_KEY=your-qwen-api-key-here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1
QWEN_MODEL=qwen-turbo
```

### 3. 测试配置

启动服务后，可以通过以下接口测试：

```bash
# 检查当前AI服务状态
curl http://localhost:3001/api/chat/health

# 查看所有提供商状态
curl http://localhost:3001/api/chat/providers

# 测试AI对话
curl -X POST http://localhost:3001/api/chat/test \
  -H "Content-Type: application/json" \
  -d '{"message": "你好，请介绍一下自己"}'
```

## 推荐配置

### 开发环境
使用OpenAI GPT-3.5-turbo，性价比高，响应快：
```bash
AI_PROVIDER=openai
OPENAI_API_KEY=sk-your-key
OPENAI_MODEL=gpt-3.5-turbo
```

### 生产环境
根据需求选择：
- **高质量对话**: GPT-4
- **中文优化**: 豆包或通义千问
- **成本控制**: GPT-3.5-turbo

## 故障排除

### 1. API密钥错误
- 检查密钥是否正确
- 确认密钥有足够的配额
- 检查网络连接

### 2. 模型不可用
- 确认模型名称正确
- 检查账户权限
- 尝试其他模型

### 3. 网络问题
- 检查防火墙设置
- 确认API端点可访问
- 考虑使用代理

## 功能特性

✅ **多提供商支持**: 轻松切换不同AI服务
✅ **自动降级**: AI服务不可用时提供备用回复
✅ **健康检查**: 实时监控服务状态
✅ **对话历史**: 支持上下文对话
✅ **错误处理**: 友好的错误提示

## 注意事项

1. **API费用**: 使用真实AI服务会产生费用，请注意用量
2. **速率限制**: 各服务商都有API调用频率限制
3. **数据隐私**: 确保遵守相关数据保护法规
4. **备份方案**: 建议配置多个提供商作为备用

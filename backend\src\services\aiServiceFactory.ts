import { BaseAIService, OpenAIService, DoubaoAIService, QwenService } from './aiService';

export class AIServiceFactory {
  static createService(): BaseAIService {
    const provider = process.env.AI_PROVIDER || 'doubao';
    
    console.log(`🤖 初始化AI服务提供商: ${provider}`);

    switch (provider.toLowerCase()) {
      case 'openai':
        return new OpenAIService({
          provider: 'openai',
          apiKey: process.env.OPENAI_API_KEY || '',
          baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
          model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo'
        });

      case 'doubao':
        return new DoubaoAIService({
          provider: 'doubao',
          apiKey: process.env.DOUBAO_API_KEY || '',
          baseUrl: process.env.DOUBAO_BASE_URL || 'https://ark.cn-beijing.volces.com',
          model: process.env.DOUBAO_MODEL || 'doubao-pro-4k'
        });

      case 'qwen':
        return new QwenService({
          provider: 'qwen',
          apiKey: process.env.QWEN_API_KEY || '',
          baseUrl: process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/api/v1',
          model: process.env.QWEN_MODEL || 'qwen-turbo'
        });

      default:
        console.warn(`⚠️ 未知的AI提供商: ${provider}，使用OpenAI作为默认选项`);
        return new OpenAIService({
          provider: 'openai',
          apiKey: process.env.OPENAI_API_KEY || '',
          baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
          model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo'
        });
    }
  }

  static async testAllServices(): Promise<{ [key: string]: boolean }> {
    const results: { [key: string]: boolean } = {};

    // 测试OpenAI
    try {
      const openaiService = new OpenAIService({
        provider: 'openai',
        apiKey: process.env.OPENAI_API_KEY || '',
        baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
        model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo'
      });
      results.openai = await openaiService.healthCheck();
    } catch (error) {
      results.openai = false;
    }

    // 测试豆包
    try {
      const doubaoService = new DoubaoAIService({
        provider: 'doubao',
        apiKey: process.env.DOUBAO_API_KEY || '',
        baseUrl: process.env.DOUBAO_BASE_URL || 'https://ark.cn-beijing.volces.com',
        model: process.env.DOUBAO_MODEL || 'doubao-pro-4k'
      });
      results.doubao = await doubaoService.healthCheck();
    } catch (error) {
      results.doubao = false;
    }

    // 测试通义千问
    try {
      const qwenService = new QwenService({
        provider: 'qwen',
        apiKey: process.env.QWEN_API_KEY || '',
        baseUrl: process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/api/v1',
        model: process.env.QWEN_MODEL || 'qwen-turbo'
      });
      results.qwen = await qwenService.healthCheck();
    } catch (error) {
      results.qwen = false;
    }

    return results;
  }
}

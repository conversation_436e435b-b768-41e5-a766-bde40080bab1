const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

async function installEdgeTTS() {
  console.log('🎵 开始安装Edge TTS...');
  
  try {
    // 检查Python是否安装
    console.log('📋 检查Python环境...');
    await execAsync('python --version');
    console.log('✅ Python已安装');
  } catch (error) {
    try {
      await execAsync('python3 --version');
      console.log('✅ Python3已安装');
    } catch (error2) {
      console.error('❌ 未找到Python，请先安装Python 3.7+');
      console.log('下载地址: https://www.python.org/downloads/');
      return;
    }
  }

  try {
    // 检查pip是否可用
    console.log('📋 检查pip...');
    await execAsync('pip --version');
    console.log('✅ pip可用');
  } catch (error) {
    try {
      await execAsync('pip3 --version');
      console.log('✅ pip3可用');
    } catch (error2) {
      console.error('❌ 未找到pip，请确保Python正确安装');
      return;
    }
  }

  try {
    // 安装edge-tts
    console.log('📦 安装edge-tts...');
    const { stdout, stderr } = await execAsync('pip install edge-tts', { timeout: 120000 });
    
    if (stderr && !stderr.includes('Successfully installed')) {
      console.log('输出:', stdout);
      console.log('错误:', stderr);
    }
    
    console.log('✅ edge-tts安装完成');
    
    // 测试安装
    console.log('🧪 测试edge-tts...');
    await execAsync('edge-tts --help', { timeout: 10000 });
    console.log('✅ edge-tts测试成功');
    
    // 列出可用的中文语音
    console.log('🎤 获取可用的中文语音...');
    const { stdout: voicesOutput } = await execAsync('edge-tts --list-voices | findstr zh-CN', { timeout: 15000 });
    console.log('可用的中文语音:');
    console.log(voicesOutput);
    
    console.log('\n🎉 Edge TTS安装成功！');
    console.log('现在可以使用高质量的语音合成功能了。');
    console.log('\n推荐的语音配置:');
    console.log('- zh-CN-XiaoxiaoNeural (女声，温柔)');
    console.log('- zh-CN-YunxiNeural (男声，年轻)');
    console.log('- zh-CN-YunyangNeural (男声，成熟)');
    
  } catch (error) {
    console.error('❌ 安装失败:', error.message);
    console.log('\n🔧 手动安装步骤:');
    console.log('1. 打开命令行');
    console.log('2. 运行: pip install edge-tts');
    console.log('3. 测试: edge-tts --help');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  installEdgeTTS();
}

module.exports = { installEdgeTTS };
